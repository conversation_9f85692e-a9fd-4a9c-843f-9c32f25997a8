const axios = require('axios');

async function testKetuaKontingenCRUD() {
  try {
    console.log('🧪 Testing Ketua Kontingen CRUD Operations...\n');

    // 1. Login as ketua-kontingen
    console.log('1. Testing login...');
    const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'kkk123'
    });
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test kontingen (should already exist)
    console.log('\n2. Testing kontingen fetch...');
    const kontingenResponse = await axios.get('http://localhost:5000/api/v1/kontingen/my', { headers });
    console.log('✅ Kontingen fetch successful:', kontingenResponse.data.data?.name || 'No kontingen');

    // 3. Test athlete creation
    console.log('\n3. Testing athlete creation...');
    const athleteData = {
      nik: Date.now().toString().slice(-16), // Generate unique NIK
      name: 'Test Athlete KKK',
      tanggal_lahir: '1995-01-01',
      jenis_kelamin: 'M',
      alamat: 'Test Address',
      no_hp: '081234567890',
      agama: 'Islam',
      umur: 29,
      berat_badan: '70',
      tinggi_badan: '175'
    };

    try {
      const athleteResponse = await axios.post('http://localhost:5000/api/v1/atlet', athleteData, { headers });
      console.log('✅ Athlete creation successful:', athleteResponse.data.success);
    } catch (athleteError) {
      console.log('❌ Athlete creation failed:', athleteError.response?.data || athleteError.message);
      console.log('Continuing with other tests...');
    }

    // 4. Test official creation
    console.log('\n4. Testing official creation...');
    const officialData = {
      name: 'Test Official KKK',
      no_hp: '081234567890',
      alamat: 'Test Address',
      agama: 'Islam',
      jenis_kelamin: 'M'
    };

    try {
      const officialResponse = await axios.post('http://localhost:5000/api/v1/official', officialData, { headers });
      console.log('✅ Official creation successful:', officialResponse.data.success);
    } catch (officialError) {
      console.log('❌ Official creation failed:', officialError.response?.data || officialError.message);
      console.log('Continuing with other tests...');
    }

    // 5. Test event registration
    console.log('\n5. Testing event registration...');
    
    // First get available events
    const eventsResponse = await axios.get('http://localhost:5000/api/v1/events', { headers });
    const events = eventsResponse.data.data.events || [];
    
    if (events.length > 0) {
      const eventId = events[0].id;
      const registrationData = { id_event: eventId };
      
      try {
        const registrationResponse = await axios.post('http://localhost:5000/api/v1/pendaftaran', registrationData, { headers });
        console.log('✅ Event registration successful:', registrationResponse.data.success);
      } catch (regError) {
        console.log('❌ Event registration failed:', regError.response?.data || regError.message);
      }
    } else {
      console.log('⚠️ No events available for registration');
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testKetuaKontingenCRUD();
