const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

async function fixAdminPassword() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '',
      database: 'db_baja_app'
    });
    
    console.log('✅ Database connection successful');
    
    // Hash the password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash('admin123456', saltRounds);
    
    // Update admin password
    await connection.execute(
      'UPDATE users SET password = ? WHERE email = ?',
      [hashedPassword, '<EMAIL>']
    );
    
    console.log('✅ Admin password updated successfully');
    
    await connection.end();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

fixAdminPassword();
