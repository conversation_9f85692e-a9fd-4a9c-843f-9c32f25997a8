const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '',
      database: 'db_baja_app'
    });
    
    console.log('✅ Database connection successful');
    
    // Show all tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('All tables in database:', tables);

    // Test if official table exists
    const [rows] = await connection.execute('SHOW TABLES LIKE "official"');
    console.log('Official table exists:', rows.length > 0);

    if (rows.length > 0) {
      // Check table structure
      const [structure] = await connection.execute('DESCRIBE official');
      console.log('Official table structure:', structure);
    }

    // Check users table
    const [users] = await connection.execute('SELECT id, name, email, role, password FROM users LIMIT 5');
    console.log('Users in database:', users);
    
    await connection.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  }
}

testConnection();
