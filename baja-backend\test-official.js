const axios = require('axios');

async function testOfficialCreation() {
  try {
    // First, let's try to login to get a token
    console.log('Testing login...');
    const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    console.log('Login successful:', loginResponse.data.success);
    const token = loginResponse.data.data.token;
    
    // Now test creating an official
    console.log('Testing official creation...');
    const officialData = {
      name: 'Test Official',
      no_hp: '081234567890',
      alamat: 'Test Address',
      agama: 'Islam',
      jenis_kelamin: 'M'
    };
    
    const officialResponse = await axios.post(
      'http://localhost:5000/api/v1/official',
      officialData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Official creation successful:', officialResponse.data);
    
  } catch (error) {
    console.error('Error occurred:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testOfficialCreation();
