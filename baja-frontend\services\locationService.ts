import { api } from './api';

export interface Negara {
  id: number;
  name: string;
}

export interface Provinsi {
  id: number;
  name: string;
  id_negara: number;
}

export interface KabupatenKota {
  id: number;
  name: string;
  id_provinsi: number;
}

class LocationService {
  async getAllNegara(): Promise<Negara[]> {
    try {
      const response = await api.get('/location/negara');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch countries');
    }
  }

  async getProvinsiByNegara(negaraId: number): Promise<Provinsi[]> {
    try {
      const response = await api.get(`/location/negara/${negaraId}/provinsi`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch provinces');
    }
  }

  async getKabupatenKotaByProvinsi(provinsiId: number): Promise<KabupatenKota[]> {
    try {
      const response = await api.get(`/location/provinsi/${provinsiId}/kabupaten-kota`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch cities/regencies');
    }
  }
}

export const locationService = new LocationService();
