const axios = require('axios');

async function testSimple() {
  try {
    // Login
    const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'kkk123'
    });
    
    const token = loginResponse.data.data.token;
    const headers = { 'Authorization': `<PERSON><PERSON> ${token}` };

    console.log('Login successful');

    // Test athlete creation with complete data
    const athleteData = {
      nik: Date.now().toString(),
      name: 'Test Athlete',
      tanggal_lahir: '1995-01-01',
      jenis_kelamin: 'M',
      alamat: 'Test Address',
      no_hp: '081234567890',
      agama: 'Islam',
      umur: 29,
      berat_badan: '70',
      tinggi_badan: '175'
    };

    console.log('Sending athlete data:', athleteData);

    try {
      const athleteResponse = await axios.post('http://localhost:5000/api/v1/atlet', athleteData, { headers });
      console.log('Athlete creation response:', athleteResponse.data);
    } catch (athleteError) {
      console.log('Athlete creation failed:', athleteError.response?.data);
    }

    // Test official creation
    const officialData = {
      name: 'Test Official',
      no_hp: '081234567890',
      alamat: 'Test Address',
      agama: 'Islam',
      jenis_kelamin: 'M'
    };

    try {
      const officialResponse = await axios.post('http://localhost:5000/api/v1/official', officialData, { headers });
      console.log('Official creation response:', officialResponse.data);
    } catch (officialError) {
      console.log('Official creation failed:', officialError.response?.data);
    }

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testSimple();
