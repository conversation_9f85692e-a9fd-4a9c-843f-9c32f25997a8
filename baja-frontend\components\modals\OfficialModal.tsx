'use client';

import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ImageUpload from '@/components/ui/ImageUpload';

interface OfficialModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  official?: {
    id: number;
    profile?: string;
    name: string;
    no_hp: string;
    alamat: string;
    agama: string;
    jenis_kelamin: 'M' | 'F';
  } | null;
}

const OfficialModal: React.FC<OfficialModalProps> = ({ isOpen, onClose, onSuccess, official }) => {
  const [formData, setFormData] = useState({
    name: '',
    no_hp: '',
    alamat: '',
    agama: '',
    jenis_kelamin: 'M' as 'M' | 'F',
  });
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (official) {
      setFormData({
        name: official.name,
        no_hp: official.no_hp,
        alamat: official.alamat,
        agama: official.agama,
        jenis_kelamin: official.jenis_kelamin,
      });
    } else {
      setFormData({
        name: '',
        no_hp: '',
        alamat: '',
        agama: '',
        jenis_kelamin: 'M',
      });
    }
    setSelectedImages([]);
    setError('');
  }, [official, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Create form data for file upload
      const submitData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        submitData.append(key, value);
      });

      // Add images if selected
      selectedImages.forEach((image) => {
        submitData.append('profile', image);
      });

      let response;
      if (official) {
        response = await api.put(`/official/${official.id}`, submitData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      } else {
        response = await api.post('/official', submitData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      if (response.data.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.data.message || 'Failed to save official');
      }
    } catch (error) {
      console.error('Error saving official:', error);
      setError('Failed to save official');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={official ? 'Edit Official' : 'Add Official'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Full Name *
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter full name"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="jenis_kelamin" className="block text-sm font-medium text-gray-300 mb-2">
              Gender *
            </label>
            <select
              id="jenis_kelamin"
              name="jenis_kelamin"
              value={formData.jenis_kelamin}
              onChange={handleChange}
              required
              disabled={loading}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
            >
              <option value="M">Male</option>
              <option value="F">Female</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="no_hp" className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number *
            </label>
            <Input
              id="no_hp"
              name="no_hp"
              type="tel"
              value={formData.no_hp}
              onChange={handleChange}
              placeholder="Enter phone number"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="agama" className="block text-sm font-medium text-gray-300 mb-2">
              Religion *
            </label>
            <Input
              id="agama"
              name="agama"
              type="text"
              value={formData.agama}
              onChange={handleChange}
              placeholder="Enter religion"
              required
              disabled={loading}
            />
          </div>
        </div>

        <div>
          <label htmlFor="alamat" className="block text-sm font-medium text-gray-300 mb-2">
            Address *
          </label>
          <textarea
            id="alamat"
            name="alamat"
            value={formData.alamat}
            onChange={handleChange}
            placeholder="Enter address"
            required
            disabled={loading}
            rows={3}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Profile Photo
          </label>
          <ImageUpload
            onImagesSelected={setSelectedImages}
            maxImages={1}
            currentImages={official?.profile ? [official.profile] : []}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-gold-500 hover:bg-gold-600 text-black"
            disabled={loading}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {official ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              official ? 'Update Official' : 'Add Official'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default OfficialModal;
